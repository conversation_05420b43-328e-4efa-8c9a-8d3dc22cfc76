                        -HC:\Users\<USER>\Downloads\flutter\packages\flutter_tools\gradle\src\main\scripts
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=26
-DANDROID_PLATFORM=android-26
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\Downloads\01 AI FITNESS APP\fitness_ai_app\android\app\build\intermediates\cxx\RelWithDebInfo\541s3q5j\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\Downloads\01 AI FITNESS APP\fitness_ai_app\android\app\build\intermediates\cxx\RelWithDebInfo\541s3q5j\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BC:\Users\<USER>\Downloads\01 AI FITNESS APP\fitness_ai_app\android\app\.cxx\RelWithDebInfo\541s3q5j\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2