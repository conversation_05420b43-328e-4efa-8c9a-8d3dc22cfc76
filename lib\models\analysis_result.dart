class AnalysisResult {
  final int correctSquats;
  final int incorrectSquats;
  final double avgKneeAngle;
  final double avgHipAngle;
  final List<String> feedback;
  final int duration; // Duration in seconds
  final int caloriesBurned; // Calculated calories
  final DateTime analysisDate;
  final String exerciseType; // Type of exercise (e.g., "Squat")

  AnalysisResult({
    required this.correctSquats,
    required this.incorrectSquats,
    required this.avgKneeAngle,
    required this.avgHipAngle,
    required this.feedback,
    required this.duration,
    required this.caloriesBurned,
    required this.analysisDate,
    this.exerciseType = "Squat",
  });

  // Calculate total squats
  int get totalSquats => correctSquats + incorrectSquats;

  // Calculate accuracy percentage
  double get accuracy => totalSquats > 0 ? (correctSquats / totalSquats) * 100 : 0;

  // Convert to database format for workouts table
  Map<String, dynamic> toWorkoutMap(int userId) {
    return {
      'user_id': userId,
      'name': '$exerciseType Analysis',
      'type': exerciseType,
      'duration': duration,
      'calories_burned': caloriesBurned,
      'date': analysisDate.toIso8601String(),
      'created_at': DateTime.now().toIso8601String(),
    };
  }

  // Convert to database format for ai_analysis table
  Map<String, dynamic> toAIAnalysisMap(int userId, int? workoutId) {
    return {
      'user_id': userId,
      'workout_id': workoutId,
      'analysis_type': exerciseType,
      'pose_data': '{"correct_reps": $correctSquats, "incorrect_reps": $incorrectSquats, "avg_knee_angle": $avgKneeAngle, "avg_hip_angle": $avgHipAngle}',
      'feedback': feedback.join('|'), // Join feedback with separator
      'score': accuracy,
      'created_at': analysisDate.toIso8601String(),
    };
  }

  // Convert to database format for exercises table
  Map<String, dynamic> toExerciseMap(int workoutId) {
    return {
      'workout_id': workoutId,
      'name': exerciseType,
      'sets': 1,
      'reps': totalSquats,
      'duration': duration,
      'notes': 'AI Analysis - Correct: $correctSquats, Incorrect: $incorrectSquats, Accuracy: ${accuracy.toStringAsFixed(1)}%',
    };
  }
}
